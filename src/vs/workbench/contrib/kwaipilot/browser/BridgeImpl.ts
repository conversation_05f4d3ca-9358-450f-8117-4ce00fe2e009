import { ICommandService, CommandsRegistry } from '../../../../platform/commands/common/commands.js';
import { registerSingleton, InstantiationType } from '../../../../platform/instantiation/common/extensions.js';
import { Disposable } from '../../../../base/common/lifecycle.js';
import {
	IBridgeService,
	BridgeMessage,
	CallbackFunction,
	HandlerFunction,
	MessageListener
} from '../common/bridgeService.js';

/**
 * Bridge 服务实现
 * 提供 WebView 和 Extension 之间的基础桥接通信功能
 * 使用单例模式管理全局桥接状态
 */
export class BridgeService extends Disposable implements IBridgeService {
	readonly _serviceBrand: undefined;

	private callbacks: Map<number, CallbackFunction>;
	private handlers: Map<string, HandlerFunction>;
	private callbackId: number;
	private messageListeners: Set<MessageListener>;

	constructor(@ICommandService private readonly commandService: ICommandService) {
		super();

		this.callbacks = new Map();
		this.handlers = new Map();
		this.callbackId = 0;
		this.messageListeners = new Set();

		// 注册桥接命令
		this._register(CommandsRegistry.registerCommand({
			id: `kwaipilot.bridge.postMessageFromExtension`,
			handler: async (accessor, message: BridgeMessage) => {
				if (message.protocol === 'callback') {
					const callback = this.callbacks.get(message.callbackId!);
					if (callback) {
						callback(message.data);
						this.callbacks.delete(message.callbackId!);
					}
				}
				else if (message.protocol === 'callHandler') {
					const handler = this.handlers.get(message.name!);
					if (handler) {
						const response = await handler(message.data);
						this.commandService.executeCommand('kwaipilot.bridge.postMessageFromUI', {
							protocol: 'callback',
							callbackId: message.callbackId,
							data: response,
						});
					}
				}
				else if (message.protocol === 'message') {
					this.messageListeners.forEach((listener) => {
						listener(message);
					});
				}
			}
		}));
	}

	callHandler(handlerName: string, data: any, callback?: CallbackFunction): void {
		const callbackId = this.callbackId++;
		if (callback) {
			this.callbacks.set(callbackId, callback);
		}

		this.commandService.executeCommand('kwaipilot.bridge.postMessageFromUI', {
			protocol: 'callHandler',
			name: handlerName,
			callbackId,
			data,
		});
	}

	registerHandler(name: string, handler: HandlerFunction): void {
		this.handlers.set(name, handler);
	}

	reigisterBridgeCommand(commandName: string, onHandler: HandlerFunction): void {
		this._register(CommandsRegistry.registerCommand({
			id: `kwaipilot.bridge.${commandName}`,
			handler: (accessor, message: BridgeMessage) => {
				return onHandler(message);
			}
		}));
	}

	postMessage(message: any): void {
		this.commandService.executeCommand('kwaipilot.bridge.postMessageFromUI', {
			protocol: 'message',
			data: message,
		});
	}

	addMessageListener(listener: MessageListener): void {
		this.messageListeners.add(listener);
	}

	removeMessageListener(listener: MessageListener): void {
		this.messageListeners.delete(listener);
	}

	/**
	 * 清理资源
	 */
	override dispose(): void {
		this.callbacks.clear();
		this.handlers.clear();
		this.messageListeners.clear();
		super.dispose();
	}
}

// 注册为单例服务
registerSingleton(IBridgeService, BridgeService, InstantiationType.Delayed);
