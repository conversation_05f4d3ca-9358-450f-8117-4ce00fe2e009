/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { URI } from '../../../../base/common/uri.js';
import { IRange } from '../../../../editor/common/core/range.js';
import { createDecorator } from '../../../../platform/instantiation/common/instantiation.js';
import { Event } from '../../../../base/common/event.js';
import { DetailedLineRangeMapping } from '../../../../editor/common/diff/rangeMapping.js';

export interface DiffChange {
	originalRange: IRange;
	modifiedRange: IRange;
	content: string;
}

export interface DiffAcceptedEvent {
	uri: URI;
	changes: DiffChange[];
}

export interface DiffRejectedEvent {
	uri: URI;
	range: IRange;
}

export interface ShowDiffParams {
	uri: URI;
	originalContent: string;
	modifiedContent: string;
	editType: string; // create | modify
	diffEnded: boolean; // s是否是流式结束后的展示
}

export interface UpdateDiffParams {
	uri: URI;
	content: string;
	isFinal: boolean;
	editType: string; // create | modify
}

export interface AcceptDiffParams {
	uri?: URI;
}

export interface RejectDiffParams {
	uri?: URI;
}

export interface DiffArea {
	id: string;
	uri: URI;
	startLine: number;
	endLine: number;
	diffs: Diff[];
}

export interface StreamingState {
	uri: URI;
	content: string;
	initialContent: string;
	isFinal: boolean;
}

export interface Diff {
	id: string;
	areaId: string;
	originalRange: IRange;
	modifiedRange: IRange;
	// 原始内容
	content: string;
	change: DetailedLineRangeMapping;
}

export const IEditCodeService = createDecorator<IEditCodeService>('editCodeService');

export interface IEditCodeService {
	readonly _serviceBrand: undefined;

	readonly onDiffAccepted: Event<DiffAcceptedEvent>;
	readonly onDiffRejected: Event<DiffRejectedEvent>;

	showDiff(params: ShowDiffParams): Promise<void>;
	acceptDiff(params: { uri: URI; range?: { startLineNumber: number; startColumn: number; endLineNumber: number; endColumn: number } }): Promise<void>;
	rejectDiff(params: { uri: URI; range?: { startLineNumber: number; startColumn: number; endLineNumber: number; endColumn: number } }): Promise<void>;
	updateDiff(params: UpdateDiffParams): Promise<void>;

	// Events for diff area management
	readonly onDidAddOrDeleteDiffAreas: Event<{ uri: URI }>;
	readonly onDidChangeDiffsInArea: Event<{ uri: URI; areaId: string }>;
	readonly onDidChangeStreamingInArea: Event<{ uri: URI; areaId: string }>;

	// Diff area management
	getDiffAreas(uri: URI): DiffArea[];
	getDiff(id: string): Diff | undefined;
	acceptAllDiffs(): Promise<void>;
	rejectAllDiffs(): Promise<void>;

	// 新增：用于获取相对路径
	getRelativePathForFile(uri: URI): string;
	// 新增：用于接受/拒绝当前代码块
	acceptCurrentChunk(editor: any, uri: URI): void;
	rejectCurrentChunk(editor: any, uri: URI): void;

}
